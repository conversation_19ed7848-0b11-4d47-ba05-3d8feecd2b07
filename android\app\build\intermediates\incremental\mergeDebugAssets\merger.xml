<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\capacitor-cordova-android-plugins\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\android\capacitor\build\intermediates\assets\debug\mergeDebugAssets"><file name="native-bridge.js" path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\android\capacitor\build\intermediates\assets\debug\mergeDebugAssets\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-local-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\local-notifications\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-geolocation" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\geolocation\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-device" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\device\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\node_modules\@capacitor\app\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/browserconfig.xml" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\browserconfig.xml"/><file name="public/cordova.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/images/icons/icon-144x144.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\icons\icon-144x144.png"/><file name="public/images/icons/icon-192x192.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\icons\icon-192x192.png"/><file name="public/images/icons/icon-48x48.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\icons\icon-48x48.png"/><file name="public/images/icons/icon-512x512.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\icons\icon-512x512.png"/><file name="public/images/icons/icon-72x72.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\icons\icon-72x72.png"/><file name="public/images/icons/icon-96x96.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\icons\icon-96x96.png"/><file name="public/images/product/img1.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\product\img1.png"/><file name="public/images/product/img2.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\product\img2.png"/><file name="public/images/product/img3.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\product\img3.png"/><file name="public/images/product/img4.png" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\images\product\img4.png"/><file name="public/index.html" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\index.html"/><file name="public/manifest.json" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\manifest.json"/><file name="public/sw.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\sw.js"/><file name="public/vite.svg" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\vite.svg"/><file name="public/assets/index-wu4JvzZ9.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\index-wu4JvzZ9.js"/><file name="public/assets/index-Xb8AcNrM.css" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\index-Xb8AcNrM.css"/><file name="public/assets/web-5r2OSrMP.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-5r2OSrMP.js"/><file name="public/assets/web-BKPcywUs.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-BKPcywUs.js"/><file name="public/assets/web-C8sB0KBF.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-C8sB0KBF.js"/><file name="public/assets/web-EZFVuX7i.js" path="C:\xampp\htdocs\ppwa\android\app\src\main\assets\public\assets\web-EZFVuX7i.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\ppwa\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>