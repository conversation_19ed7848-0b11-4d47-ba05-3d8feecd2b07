import React from 'react';
import './Layanan.css';

/**
 * Interface untuk data layanan
 */
interface LayananItem {
  id: string;
  title: string;
  image: string;
  description: string;
  features: string[];
}

/**
 * Komponen Layanan - Menampilkan layanan perusahaan
 * Layout berdasarkan gambar referensi dengan 3 kartu layanan
 */
const Layanan: React.FC = () => {
  // Data layanan berdasarkan gambar referensi
  const layananData: LayananItem[] = [
    {
      id: 'ac-service',
      title: 'Air Conditioner Service',
      image: '/images/layanan/ac-service.jpg',
      description: 'General Service & Maintenance Auto Air Conditioner System',
      features: [
        'Alat support & service r/v, bus, dll',
        'Alat berat produksi Excavator, Dozer',
        'Alat berat produksi Crane',
        'Service peralatan pendukung pertambangan'
      ]
    },
    {
      id: 'ban-velg',
      title: 'Ban & Velg (Tyre)',
      image: '/images/layanan/ban-velg.jpg',
      description: 'Support Repair & Rental Tyre',
      features: [
        'Penyediaan ban baru - bekas berkualitas',
        'Service ban bocor, ganti ban baru',
        'Penyediaan ban untuk berbagai jenis kendaraan',
        'Berbagai ukuran ban sesuai kebutuhan',
        'Solusi perawatan ban untuk heavy equipment',
        'Rental ban untuk proyek jangka pendek',
        'Analisa'
      ]
    },
    {
      id: 'fabrikasi-welding',
      title: 'Fabrikasi & Welding',
      image: '/images/layanan/fabrikasi-welding.jpg',
      description: 'Welding, Repair & Fabrikasi Professional Peralatan Industri',
      features: [
        'Modifikasi alat berat sesuai kebutuhan',
        'Repair cepat, tools, unit attachment',
        'Fabrikasi komponen khusus',
        'Fabrikasi komponen khusus',
        'Pengelasan & finishing berkualitas'
      ]
    }
  ];

  /**
   * Navigate to home
   */
  const navigateToHome = () => {
    window.dispatchEvent(new CustomEvent('navigateToHome'));
  };

  return (
    <div className="layanan-container">
      {/* Header dengan logo perusahaan - consistent with Calendar */}
      <header className="layanan-header">
        <div className="flex items-center gap-4">
          <img
            src="/images/icons/icon-192x192.png"
            alt="PT Putera Wibowo Borneo Logo"
            className="w-10 h-10 object-contain rounded-lg shadow-lg shadow-primary/20"
          />
          <div>
            <h1 className="text-xl font-semibold text-primary tracking-wide">
              PT PUTERA WIBOWO BORNEO
            </h1>
            <p className="text-sm text-base-content/70 italic">
              "Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="layanan-main">
        {/* Page Title */}
        <div className="layanan-title-section">
          <h2 className="layanan-title">LAYANAN</h2>
        </div>

        {/* Services Grid */}
        <div className="layanan-grid">
          {layananData.map((layanan) => (
            <div key={layanan.id} className="layanan-card">
              {/* Service Image */}
              <div className="layanan-image-container">
                <img
                  src={layanan.image}
                  alt={layanan.title}
                  className="layanan-image"
                  onError={(e) => {
                    // Fallback image if service image not found
                    (e.target as HTMLImageElement).src = '/images/icons/icon-192x192.png';
                  }}
                />
              </div>

              {/* Service Content */}
              <div className="layanan-content">
                {/* Service Icon and Title */}
                <div className="layanan-header-section">
                  <div className="layanan-icon">
                    {layanan.id === 'ac-service' && '❄️'}
                    {layanan.id === 'ban-velg' && '🚗'}
                    {layanan.id === 'fabrikasi-welding' && '🔧'}
                  </div>
                  <h3 className="layanan-card-title">{layanan.title}</h3>
                </div>

                {/* Service Description */}
                <p className="layanan-description">{layanan.description}</p>

                {/* Service Features */}
                <ul className="layanan-features">
                  {layanan.features.map((feature, index) => (
                    <li key={index} className="layanan-feature-item">
                      <span className="layanan-feature-icon">✓</span>
                      <span className="layanan-feature-text">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Back to Home Button */}
        <div className="layanan-navigation">
          <button
            onClick={navigateToHome}
            className="btn-back-home"
          >
            <span className="btn-icon">🏠</span>
            <span>Kembali ke HOME</span>
          </button>
        </div>
      </main>
    </div>
  );
};

export default Layanan;
